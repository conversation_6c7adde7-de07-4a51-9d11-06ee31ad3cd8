package com.holderzone.holder.saas.aggregation.app.controller.organization;

import com.alibaba.fastjson.JSON;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.cloud.EnterpriseCloudService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.organization.OrgFeignClient;
import com.holderzone.resource.common.dto.enterprise.MultiMemberQueryDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationDTO;
import com.holderzone.saas.store.dto.common.BaseRespDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.table.PadAreaDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.enums.common.ResultStateEnum;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 组织服务相关
 * @date 2021/5/12 17:31
 */
@RestController
@Api(tags = "品牌、组织、门店相关接口", description = "商户后台二期品牌、组织、门店相关服务接口")
@Slf4j
@AllArgsConstructor
public class OrganizationController {

    private final OrgFeignClient orgFeignClient;

    private final EnterpriseCloudService enterpriseCloudService;

    /**
     * holder-saas-member-terminal
     */
    private final NewMemberInfoClientService memberInfoClientService;

    @ApiOperation(value = "获取企业下的所有门店", notes = "获取企业下的所有门店")
    @PostMapping(value = "/store/query_all_store")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "获取企业下的所有门店")
    public Result<List<StoreDTO>> queryAllStore() {
        return Result.buildSuccessResult(orgFeignClient.queryAllStore().stream()
                .filter(storeDTO -> !storeDTO.getIsDeleted())
                .collect(Collectors.toList()));
    }

    /**
     * 查询运营主体下的门店列表
     *
     * @param queryDTO 关联企业guid，运营主体guid
     * @return 门店列表
     */
    @ApiOperation(value = "查询运营主体下的门店列表", notes = "必传 关联企业guid，运营主体guid")
    @PostMapping("/organization/store_list_multiMemberGuid")
    public Result<List<OrganizationDTO>> getStoreByMultiMemberGuid(@RequestBody MultiMemberQueryDTO queryDTO) {
        log.info("查询运营主体下的门店列表 入参:{}", JSON.toJSONString(queryDTO));
        return Result.buildSuccessResult(enterpriseCloudService.getStoreByMultiMemberGuid(queryDTO));
    }

    @ApiOperation(value = "根据门店guid集合及日期时间获取所属营业日期")
    @PostMapping("store/query_business_day")
    public Result<LocalDate> queryBusinessDay(@RequestBody BusinessDateReqDTO businessDateReqDTO) {
        return Result.buildSuccessResult(orgFeignClient.queryBusinessDay(businessDateReqDTO));
    }

    @ApiOperation(value = "查询pad点餐模式")
    @PostMapping(value = "device/query_pad_order_type")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    public Result<PadOrderTypeRespDTO> queryPadOrderType(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("查询pad点餐模式 {}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return Result.buildSuccessResult(orgFeignClient.queryPadOrderType(padOrderTypeReqDTO));
    }

    @ApiOperation(value = "查询已绑定的桌台信息")
    @PostMapping(value = "device/query_binding_table_info")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    public Result<TableInfoDTO> queryBindingTableInfo(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("查询已绑定的桌台信息入参 {}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return Result.buildSuccessResult(orgFeignClient.queryBindingTableInfo(padOrderTypeReqDTO));
    }

    /**
     * 查询已绑定的桌台信息
     *
     * @param padOrderTypeReqDTO pad点餐查询
     * @return 只返回未开台
     */
    @ApiOperation(value = "查询已绑定的桌台信息")
    @PostMapping(value = "device/query_un_binding_table_info")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    public Result<List<PadAreaDTO>> queryUnBindingTableInfo(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("查询已绑定的桌台信息入参 {}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        return Result.buildSuccessResult(orgFeignClient.queryUnBindingTableInfo(padOrderTypeReqDTO));
    }


    @ApiOperation(value = "设置pad点餐模式")
    @PostMapping(value = "device/pad_order_type_set")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    public Result padOrderTypeSet(@RequestBody @Validated PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("设置pad点餐模式入参：{}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        boolean result = orgFeignClient.padOrderTypeSet(padOrderTypeReqDTO);
        if (result) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult("更新失败");
        }
    }

    @ApiOperation(value = "初始化pad点餐模式设置")
    @PostMapping(value = "device/initialize_pad_order_type_set")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    public Result initializePadOrderTypeSet(@RequestBody @Validated PadOrderTypeReqDTO padOrderTypeReqDTO) {
        log.info("初始化pad点餐模式设置入参：{}", JacksonUtils.writeValueAsString(padOrderTypeReqDTO));
        boolean result = orgFeignClient.initializePadOrderTypeSet(padOrderTypeReqDTO);
        if (result) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult("更新失败");
        }
    }

    @ApiOperation(value = "查询pad开始点餐返回信息")
    @PostMapping(value = "store/get_pad_start_order_info")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "storeGuid", value = "门店guid")
    })
    public Result<PadStartOrderRespDTO> getPadStartOrderInfo(@RequestParam("storeGuid") String storeGuid) {
        String operSubjectGuid = UserContextUtils.get().getOperSubjectGuid();
        log.info("查询pad开始点餐返回信息 storeGuid={},operSubjectGuid={}", storeGuid, operSubjectGuid);
        PadStartOrderRespDTO padStartOrderInfo = orgFeignClient.getPadStartOrderInfo(storeGuid);

        // 查询能否使用积分
        BaseRespDTO baseRespDTO = memberInfoClientService.queryDeductionRule(operSubjectGuid);
        log.info("baseRespDTO={}", JacksonUtils.writeValueAsString(baseRespDTO));
        padStartOrderInfo.setCanUseIntegral(Objects.equals(ResultStateEnum.SUCCESS.getCode(), baseRespDTO.getResultState())
                ? Boolean.TRUE : Boolean.FALSE);
        return Result.buildSuccessResult(padStartOrderInfo);
    }

    @ApiOperation("查询餐段")
    @GetMapping("/store/query_meal_section")
    public Result<List<StoreMealSectionDTO>> queryMealSection(@RequestParam("storeGuid") String storeGuid) {
        List<StoreMealSectionDTO> storeMealSectionDTOS = orgFeignClient.queryMealSection(storeGuid);
        return Result.buildSuccessResult(storeMealSectionDTOS);
    }
}