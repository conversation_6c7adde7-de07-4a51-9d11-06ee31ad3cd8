package com.holderzone.saas.store.enums.store;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/8/7
 * @since 1.8
 */
@Getter
public enum MealSectionEnum {
    MEAL_SECTION_MORNING(1, "早市"),
    MEAL_SECTION_NOON(2, "午市"),
    MEAL_SECTION_AFTERNOON(3, "下午茶"),
    MEAL_SECTION_EVENING(4, "晚市"),
    MEAL_SECTION_NIGHT(5, "夜宵");

    private final int code;
    private final String desc;

    MealSectionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        if (Objects.isNull(code)) {
            return StringUtils.EMPTY;
        }
        for (MealSectionEnum value : values()) {
            if (value.code == code) {
                return value.desc;
            }
        }
        return StringUtils.EMPTY;
    }

    public static Integer getCode(String desc) {
        if (StringUtils.isEmpty(desc)) {
            return null;
        }
        for (MealSectionEnum value : values()) {
            if (value.desc.equals(desc)) {
                return value.code;
            }
        }
        return null;
    }
}
