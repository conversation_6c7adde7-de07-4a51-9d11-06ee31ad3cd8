package com.holderzone.saas.store.enums.print;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessTypeEnum
 * @date 2018/9/29 16:50
 * @description 打印业务类型
 * @program holder-saas-store-print
 */
public enum BusinessTypeEnum {

    FRONT_PRINTER(0, "前台"),

    KITCHEN_PRINTER(1, "后厨"),

    LABEL_PRINTER(2, "标签"),

    COMBINATION(3, "组合"),
    ;

    private final Integer type;

    private final String desc;

    BusinessTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static BusinessTypeEnum ofType(Integer type) {
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (businessTypeEnum.getType().equals(type)) {
                return businessTypeEnum;
            }
        }
        throw new BusinessException("目前仅支持前台、后厨、标签");
    }

    /**
     * 前台
     */
    private final static List<Integer> FRONT_PRINTER_LIST;

    /**
     * 后厨
     */
    private final static List<Integer> KITCHEN_PRINTER_LIST;

    static {
        FRONT_PRINTER_LIST = Lists.newArrayList(
                InvoiceTypeEnum.CHECKOUT.getType(),
                InvoiceTypeEnum.CHECKOUT_TABLES.getType()
        );
        KITCHEN_PRINTER_LIST = Lists.newArrayList(
                InvoiceTypeEnum.ORDER_ITEM.getType(),
                InvoiceTypeEnum.ORDER_ITEM_TAKEAWAY.getType(),
                InvoiceTypeEnum.REFUND_ITEM.getType()
        );
    }

    public static BusinessTypeEnum ofInvoiceType(Integer invoiceType) {
        if (FRONT_PRINTER_LIST.contains(invoiceType)){
           return BusinessTypeEnum.FRONT_PRINTER;
        }
        if (KITCHEN_PRINTER_LIST.contains(invoiceType)){
            return BusinessTypeEnum.KITCHEN_PRINTER;
        }
        throw new BusinessException("目前仅支持前台、后厨");
    }
}
