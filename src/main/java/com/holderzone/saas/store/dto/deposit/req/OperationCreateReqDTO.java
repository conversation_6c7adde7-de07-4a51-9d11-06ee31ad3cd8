package com.holderzone.saas.store.dto.deposit.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class OperationCreateReqDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @NotEmpty(message = "操作方式不得为空，标记是存入还是取出")
    @ApiModelProperty(value = "操作方式，0：存入，1：取出")
    private int operationWay;

    @NotEmpty(message = "操作人Id不得为空")
    @ApiModelProperty(value = "操作人Id")
    private String userId;

    @NotEmpty(message = "操作人不得为空")
    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;
}