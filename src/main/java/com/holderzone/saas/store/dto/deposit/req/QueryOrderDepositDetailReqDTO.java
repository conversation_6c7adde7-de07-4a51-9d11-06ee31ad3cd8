package com.holderzone.saas.store.dto.deposit.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 查询订单寄存记录详情请求
 *
 * <AUTHOR>
 * @since 2025/8/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrderDepositDetailReqDTO {

    @ApiModelProperty(value = "订单Guid")
    @NotNull(message = "订单Guid不得为空")
    private String orderGuid;
}
