package com.holderzone.saas.store.dto.item.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 菜谱方案商品分类
 *
 * <AUTHOR>
 * @date 2025/7/28
 * @since 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemTypeListDTO {

    /**
     * 菜品商品guid
     */
    private String pricePlanItemGuid;

    /**
     * 商品类型Guid
     */
    private String typeGuid;

    /**
     * 商品类型名称
     */
    private String typeName;

    /**
     * 排序
     */
    private Integer sort;

    public ItemTypeListDTO(String pricePlanItemGuid, String typeGuid, Integer sort) {
        this.pricePlanItemGuid = pricePlanItemGuid;
        this.typeGuid = typeGuid;
        this.sort = sort;
    }
}
