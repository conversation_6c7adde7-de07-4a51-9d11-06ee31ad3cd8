package com.holderzone.saas.store.dto.deposit.req;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 订单部分退款DTO
 *
 * <AUTHOR>
 * @since 2025/8/10
 */
@Data
public class OrderRefundDepositReqDTO {

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单商品")
    private List<DineInItemDTO> itemDTOS;

    @ApiModelProperty(value = "订单退款商品")
    private List<DineInItemDTO> refundItemDTOS;
}
