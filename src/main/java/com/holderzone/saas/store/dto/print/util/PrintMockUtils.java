package com.holderzone.saas.store.dto.print.util;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.response.CustomizeLabelDetails;
import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalDetailsDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalFieldDetailsDTO;
import com.holderzone.saas.store.dto.print.content.*;
import com.holderzone.saas.store.dto.print.content.nested.*;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@SuppressWarnings("unchecked")
public final class PrintMockUtils {

    public static final String MOCK_TAKEOUT_PLATFORM = "美团外卖";

    public static final String MOCK_TAKEOUT_PLATFORM_ORDER = "#5";

    public static final String MOCK_TAKEOUT_PAY_MSG = "已在线支付";

    public static final String MOCK_TAKEOUT_EXPECT_TIME = "立即送达";

    public static final String MOCK_TAKEOUT_REMARK = "红油饺子真的太好吃了，收新人隐私号 17882958153，手机号177****5065 顾客满要餐具";

    public static final BigDecimal MOCK_TAKEOUT_ITEM_TOTAL_PRICE = new BigDecimal("12");

    public static final BigDecimal MOCK_TAKEOUT_ORIGINAL_PRICE = new BigDecimal("16");

    public static final BigDecimal MOCK_TAKEOUT_ACTUALLY_PAY = new BigDecimal("14");

    public static final String MOCK_TAKEOUT_RECEIVER_NAME = "XX (先生)";

    public static final String MOCK_TAKEOUT_RECEIVER_TEL = "[212141223121532121]";

    public static final String MOCK_TAKEOUT_RECEIVER_ADDRESS = "测试地址xx号xx楼";

    public static final String MOCK_TAKEOUT_FULL_GIFT = "满1.0元赠一分钱商品 1份";

    public static String getMockContent(Integer invoiceType) {
        PrintDTO printDTO = setMockContent(invoiceType);
        setMockItemRecords(printDTO);
        setMockBaseInfo(printDTO, invoiceType);
        return JacksonUtils.writeValueAsString(printDTO);
    }

    public static PrintDTO setMockContent(Integer invoiceType) {
        switch (InvoiceTypeEnum.ofType(invoiceType)) {
            case ITEM_LIST: {
                PrintItemDetailDTO itemDetailFormatDTO = new PrintItemDetailDTO();
                itemDetailFormatDTO.setStoreName(mockStoreName());
                itemDetailFormatDTO.setOrderRemark(mockOrderRemark());
                itemDetailFormatDTO.setMarkNo(mockMarkNo());
                itemDetailFormatDTO.setOrderNo(mockOrderNo());
                itemDetailFormatDTO.setPersonNumber(mockPersonNumber());
                itemDetailFormatDTO.setOpenTableTime(mockOpenTableTime());
                itemDetailFormatDTO.setTotal(mockTotal());
                itemDetailFormatDTO.setMemberPortrayalDTO(mockMemberPortrayalDTO());
                return itemDetailFormatDTO;
            }
            case PRE_CHECKOUT: {
                PrintPreCheckoutDTO printPreCheckoutDTO = new PrintPreCheckoutDTO();
                printPreCheckoutDTO.setAdditionalChargeList(mockAdditionalChargeList());
                printPreCheckoutDTO.setReduceRecordList(mockReduceRecordList());
                printPreCheckoutDTO.setPayAble(mockPayable());
                printPreCheckoutDTO.setStoreAddress(mockStoreAddress());
                printPreCheckoutDTO.setTel(mockStoreTel());
                printPreCheckoutDTO.setStoreName(mockStoreName());
                printPreCheckoutDTO.setOrderRemark(mockOrderRemark());
                printPreCheckoutDTO.setMarkNo(mockMarkNo());
                printPreCheckoutDTO.setOrderNo(mockOrderNo());
                printPreCheckoutDTO.setPersonNumber(mockPersonNumber());
                printPreCheckoutDTO.setOpenTableTime(mockOpenTableTime());
                printPreCheckoutDTO.setTotal(mockTotal());
                return printPreCheckoutDTO;
            }
            case PRE_CHECKOUT_TABLES: {
                PrintPreCoTableCbDTO printPreCoTableCbDTO = new PrintPreCoTableCbDTO();
                printPreCoTableCbDTO.setStoreName(mockStoreName());
                printPreCoTableCbDTO.setTableOrderList(mockPreTableOrders());
                printPreCoTableCbDTO.setTableTotal(mockTotal());
                printPreCoTableCbDTO.setAdditionalChargeList(mockAdditionalChargeList());
                printPreCoTableCbDTO.setReduceRecordList(mockReduceRecordList());
                printPreCoTableCbDTO.setPayAble(mockPayable());
                printPreCoTableCbDTO.setStoreAddress(mockStoreAddress());
                printPreCoTableCbDTO.setTel(mockStoreTel());
                return printPreCoTableCbDTO;
            }
            case CHECKOUT: {
                PrintCheckOutDTO printCheckOutDTO = new PrintCheckOutDTO();
                printCheckOutDTO.setCheckOutTime(mockCheckoutTime());
                printCheckOutDTO.setChangedPay(mockPayChanged());
                printCheckOutDTO.setActuallyPay(mockActuallyPay());
                printCheckOutDTO.setPayRecordList(mockPayRecords());
                printCheckOutDTO.setTradeMode(0);
                printCheckOutDTO.setAdditionalChargeList(mockAdditionalChargeList());
                printCheckOutDTO.setReduceRecordList(mockReduceRecordList());
                printCheckOutDTO.setPayAble(mockPayable());
                printCheckOutDTO.setStoreAddress(mockStoreAddress());
                printCheckOutDTO.setTel(mockStoreTel());
                printCheckOutDTO.setStoreName(mockStoreName());
                printCheckOutDTO.setOrderRemark(mockOrderRemark());
                printCheckOutDTO.setMarkNo(mockMarkNo());
                printCheckOutDTO.setOrderNo(mockOrderNo());
                printCheckOutDTO.setPersonNumber(mockPersonNumber());
                printCheckOutDTO.setOpenTableTime(mockOpenTableTime());
                printCheckOutDTO.setTotal(mockTotal());
                return printCheckOutDTO;
            }
            case CHECKOUT_TABLES: {
                PrintCoTableCbDTO printCoTableCbDTO = new PrintCoTableCbDTO();
                printCoTableCbDTO.setStoreName(mockStoreName());
                printCoTableCbDTO.setTableOrderList(mockNowTableOrders());
                printCoTableCbDTO.setTableTotal(mockTotal());
                printCoTableCbDTO.setAdditionalChargeList(mockAdditionalChargeList());
                printCoTableCbDTO.setReduceRecordList(mockReduceRecordList());
                printCoTableCbDTO.setPayAble(mockPayable());
                printCoTableCbDTO.setChangedPay(mockPayChanged());
                printCoTableCbDTO.setActuallyPay(mockActuallyPay());
                printCoTableCbDTO.setPayRecordList(mockPayRecords());
                printCoTableCbDTO.setStoreAddress(mockStoreAddress());
                printCoTableCbDTO.setTel(mockStoreTel());
                return printCoTableCbDTO;
            }
            case STORED_CASH: {
                PrintStoredCashDTO printStoredCashDTO = new PrintStoredCashDTO();
                printStoredCashDTO.setStoreName(mockStoreName());
                printStoredCashDTO.setSerialNumber("0123456789");
                printStoredCashDTO.setRecharge(BigDecimal.ONE);
                printStoredCashDTO.setPresented(BigDecimal.ONE);
                printStoredCashDTO.setPresentedIntegral(BigDecimal.ONE);
                printStoredCashDTO.setArrival(BigDecimal.ONE);
                printStoredCashDTO.setPayWay("现金支付");
                printStoredCashDTO.setCardNo("123456");
                printStoredCashDTO.setCurrentCash(BigDecimal.ONE);
                printStoredCashDTO.setIntegration("123");
                printStoredCashDTO.setRechargeTime(DateTimeUtils.nowMillis());
                printStoredCashDTO.setStoreAddress(mockStoreAddress());
                printStoredCashDTO.setTel(mockStoreTel());
                return printStoredCashDTO;
            }
            case TURN_TABLE:
            case TURN_TABLE_ITEM: {
                PrintTurnTableDTO printTurnTableDTO = new PrintTurnTableDTO();
                printTurnTableDTO.setStoreName(mockStoreName());
                printTurnTableDTO.setSrcTableName("测试桌台1");
                printTurnTableDTO.setDestTableName("测试桌台2");
                printTurnTableDTO.setTurnTime(DateTimeUtils.nowMillis());
                return printTurnTableDTO;
            }
            case ORDER_ITEM:
            case ORDER_ITEM_TAKEAWAY: {
                PrintOrderItemDTO printOrderItemDTO = new PrintOrderItemDTO();
                printOrderItemDTO.setEstimateDeliveredTimeString("立即送达");
                printOrderItemDTO.setRemark(mockOrderRemark());
                printOrderItemDTO.setMarkNo(mockMarkNo());
                printOrderItemDTO.setOrderNo(mockOrderNo());
                printOrderItemDTO.setFoodFinishBarCode(mockFoodFinishBarCode());
                printOrderItemDTO.setPersonNumber(mockPersonNumber());
                printOrderItemDTO.setOrderTime(mockOpenTableTime());
                printOrderItemDTO.setTradeMode(0);
                return printOrderItemDTO;
            }
            case REFUND_ITEM: {
                PrintRefundItemDTO printRefundItemDTO = new PrintRefundItemDTO();
                printRefundItemDTO.setMarkNo(mockMarkNo());
                printRefundItemDTO.setOrderNo(mockOrderNo());
                printRefundItemDTO.setPersonNumber(mockPersonNumber());
                printRefundItemDTO.setOrderTime(mockOpenTableTime());
                printRefundItemDTO.setTradeMode(0);
                return printRefundItemDTO;
            }
            default:
                throw new BusinessException("该模板不支持自定义");
        }
    }

    public static void setMockItemRecords(PrintDTO printDTO) {
        if (printDTO instanceof PrintBaseItemDTO) {
            ((PrintBaseItemDTO) printDTO).setItemRecordList(mockItemRecords());
        }
        if (printDTO instanceof PrintPreCoTableCbDTO) {
            ((PrintPreCoTableCbDTO) printDTO).getTableOrderList()
                    .forEach(printTableDTO -> printTableDTO.setPrintItemRecordList(mockItemRecords()));
        }
        if (printDTO instanceof PrintCoTableCbDTO) {
            ((PrintCoTableCbDTO) printDTO).getTableOrderList()
                    .forEach(printTableDTO -> printTableDTO.setPrintItemRecordList(mockItemRecords()));
        }
    }

    public static List<PrintItemRecord> mockItemRecords() {
        PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setItemName("测试商品");
        printItemRecord.setItemTypeName("测试分类");
        printItemRecord.setItemTypeGuid("20230608");
        printItemRecord.setPrice(BigDecimal.valueOf(12.00));
        printItemRecord.setNumber(BigDecimal.valueOf(12.00));
        printItemRecord.setUnit("份");
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setRemark("不放辣椒");
        printItemRecord.setProperty("微辣");
        printItemRecord.setPropertyPrice(BigDecimal.ZERO);
        printItemRecord.setIngredientPrice(BigDecimal.ZERO);
        printItemRecord.setItemTotal(BigDecimal.valueOf(12.00));
        return Collections.singletonList(printItemRecord);
    }

    public static List<PrintItemRecord> mockTakeoutItemRecords() {
        PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setItemName("夫妻肺片");
        printItemRecord.setItemTypeName("凉菜");
        printItemRecord.setItemTypeGuid("1001001000");
        printItemRecord.setPrice(new BigDecimal("12"));
        printItemRecord.setNumber(new BigDecimal("1"));
        printItemRecord.setUnit("份");
        printItemRecord.setAsWeight(false);
        printItemRecord.setAsPackage(false);
        printItemRecord.setAsGift(false);
        printItemRecord.setRemark("不要蒜");
        printItemRecord.setProperty("微辣");
        printItemRecord.setPropertyPrice(BigDecimal.ZERO);
        printItemRecord.setIngredientPrice(BigDecimal.ZERO);
        printItemRecord.setItemTotal(new BigDecimal("12"));
        printItemRecord.setCartId(0);
        return Collections.singletonList(printItemRecord);
    }

    public static void setMockBaseInfo(PrintDTO printDTO, Integer invoiceType) {
        printDTO.setInvoiceType(invoiceType);
        printDTO.setOperatorStaffName("管理员");
        printDTO.setCreateTime(DateTimeUtils.nowMillis());
    }

    public static String mockStoreName() {
        return "成都味道太古里店";
    }

    public static String mockOrderNo() {
        return "201905051234";
    }

    public static String mockFoodFinishBarCode() {
        return "8063830068861791617";
    }

    public static String mockOrderRemark() {
        return "先上凉菜";
    }

    public static String mockMarkNo() {
        return "A013";
    }

    public static int mockPersonNumber() {
        return 5;
    }

    public static long mockOpenTableTime() {
        return DateTimeUtils.nowMillis();
    }

    public static BigDecimal mockTotal() {
        return BigDecimal.valueOf(27.00);
    }

    public static MemberPortrayalDetailsDTO mockMemberPortrayalDTO() {
        MemberPortrayalDetailsDTO detailsDTO = new MemberPortrayalDetailsDTO();
        detailsDTO.setMemberGuid("7085093153023197184");
        detailsDTO.setMemberName("热心市民");
        detailsDTO.setMemberPhone("182****0085");
        detailsDTO.setHeadImgUrl("https://thirdwx.qlogo.cn/mmopen/vi_32/0K42d2gvYoCcteevvEJ7rcR3RFBp9zM6BlkWhfeMOa7hSaQqUdGthAPuTTOSs7KG2SsRiarhcXTl91vINMfU7hA/132");

        // 设置 basicsFieldList
        List<MemberPortrayalFieldDetailsDTO> basicsFieldList = new ArrayList<>();
        MemberPortrayalFieldDetailsDTO basicsField1 = new MemberPortrayalFieldDetailsDTO();
        basicsField1.setField("BIRTHDAY");
        basicsField1.setFieldName("生日");
        basicsField1.setFieldValue("-");
        basicsField1.setFieldType(1);
        basicsField1.setFieldSort(1);
        basicsFieldList.add(basicsField1);

        MemberPortrayalFieldDetailsDTO basicsField2 = new MemberPortrayalFieldDetailsDTO();
        basicsField2.setField("MEMBER_GRADE");
        basicsField2.setFieldName("会员等级");
        basicsField2.setFieldValue("-");
        basicsField2.setFieldType(1);
        basicsField2.setFieldSort(2);
        basicsFieldList.add(basicsField2);

        MemberPortrayalFieldDetailsDTO basicsField3 = new MemberPortrayalFieldDetailsDTO();
        basicsField3.setField("REGISTER_TIME");
        basicsField3.setFieldName("注册时间");
        basicsField3.setFieldValue("2023-07-13 11:10:43");
        basicsField3.setFieldType(1);
        basicsField3.setFieldSort(3);
        basicsFieldList.add(basicsField3);

        MemberPortrayalFieldDetailsDTO basicsField4 = new MemberPortrayalFieldDetailsDTO();
        basicsField4.setField("GENDER");
        basicsField4.setFieldName("性别");
        basicsField4.setFieldValue("男性");
        basicsField4.setFieldType(1);
        basicsField4.setFieldSort(4);
        basicsFieldList.add(basicsField4);

        detailsDTO.setBasicsFieldList(basicsFieldList);

        // 设置 consumeFieldList
        List<MemberPortrayalFieldDetailsDTO> consumeFieldList = new ArrayList<>();
        MemberPortrayalFieldDetailsDTO consumeField1 = new MemberPortrayalFieldDetailsDTO();
        consumeField1.setField("LAST_CONSUME_TIME");
        consumeField1.setFieldName("上次消费时间");
        consumeField1.setFieldValue("2024-12-21 18:53:29");
        consumeField1.setFieldType(2);
        consumeField1.setFieldSort(1);
        consumeField1.setIsSelectAmountLimit(0);
        consumeField1.setAmountLimit(BigDecimal.ZERO);
        consumeFieldList.add(consumeField1);

        MemberPortrayalFieldDetailsDTO consumeField2 = new MemberPortrayalFieldDetailsDTO();
        consumeField2.setField("GUEST_SINGLE_PRICE");
        consumeField2.setFieldName("客单价");
        consumeField2.setFieldValue("42.36");
        consumeField2.setFieldType(2);
        consumeField2.setFieldSort(2);
        consumeField2.setStatisticalPeriod(2);
        consumeField2.setRecentDays(10);
        consumeField2.setIsSelectAmountLimit(1);
        consumeField2.setAmountLimit(new BigDecimal("10.00"));
        consumeFieldList.add(consumeField2);

        MemberPortrayalFieldDetailsDTO consumeField3 = new MemberPortrayalFieldDetailsDTO();
        consumeField3.setField("CONSUME_COUNT");
        consumeField3.setFieldName("累计消费次数");
        consumeField3.setFieldValue("0");
        consumeField3.setFieldType(2);
        consumeField3.setFieldSort(3);
        consumeField3.setStatisticalPeriod(2);
        consumeField3.setRecentDays(3);
        consumeField3.setIsSelectAmountLimit(1);
        consumeField3.setAmountLimit(new BigDecimal("5.00"));

        List<CustomizeLabelDetails> customizeLabelList = new ArrayList<>();
        CustomizeLabelDetails details1 = new CustomizeLabelDetails();
        details1.setLabelName("新客");
        details1.setIncludeEndNum(3);
        customizeLabelList.add(details1);

        CustomizeLabelDetails details2 = new CustomizeLabelDetails();
        details2.setLabelName("熟客");
        details2.setIncludeStartNum(5);
        details2.setIncludeEndNum(8);
        customizeLabelList.add(details2);

        CustomizeLabelDetails details3 = new CustomizeLabelDetails();
        details3.setLabelName("贵客");
        details3.setIncludeStartNum(10);
        customizeLabelList.add(details3);

        consumeField3.setCustomizeLabelList(customizeLabelList);
        consumeFieldList.add(consumeField3);

        MemberPortrayalFieldDetailsDTO consumeField4 = new MemberPortrayalFieldDetailsDTO();
        consumeField4.setField("CONSUME_AMOUNT");
        consumeField4.setFieldName("累计消费金额");
        consumeField4.setFieldValue("533.84");
        consumeField4.setFieldType(2);
        consumeField4.setFieldSort(4);
        consumeField4.setStatisticalPeriod(2);
        consumeField4.setRecentDays(20);
        consumeField4.setIsSelectAmountLimit(0);
        consumeField4.setAmountLimit(BigDecimal.ZERO);
        consumeFieldList.add(consumeField4);

        detailsDTO.setConsumeFieldList(consumeFieldList);

        // 设置 rechargeFieldList
        List<MemberPortrayalFieldDetailsDTO> rechargeFieldList = new ArrayList<>();
        MemberPortrayalFieldDetailsDTO rechargeField1 = new MemberPortrayalFieldDetailsDTO();
        rechargeField1.setField("RECHARGE_AMOUNT");
        rechargeField1.setFieldName("累计充值金额");
        rechargeField1.setFieldValue("162.07");
        rechargeField1.setFieldType(3);
        rechargeField1.setFieldSort(1);
        rechargeField1.setStatisticalPeriod(2);
        rechargeField1.setRecentDays(365);
        rechargeField1.setIsSelectAmountLimit(0);
        rechargeField1.setAmountLimit(BigDecimal.ZERO);
        rechargeFieldList.add(rechargeField1);

        MemberPortrayalFieldDetailsDTO rechargeField2 = new MemberPortrayalFieldDetailsDTO();
        rechargeField2.setField("AVERAGE_RECHARGE_AMOUNT");
        rechargeField2.setFieldName("次均充值金额");
        rechargeField2.setFieldValue("1.00");
        rechargeField2.setFieldType(3);
        rechargeField2.setFieldSort(2);
        rechargeField2.setStatisticalPeriod(2);
        rechargeField2.setRecentDays(5);
        rechargeField2.setIsSelectAmountLimit(0);
        rechargeField2.setAmountLimit(BigDecimal.ZERO);
        rechargeFieldList.add(rechargeField2);

        MemberPortrayalFieldDetailsDTO rechargeField3 = new MemberPortrayalFieldDetailsDTO();
        rechargeField3.setField("LAST_RECHARGE_TIME");
        rechargeField3.setFieldName("上次充值时间");
        rechargeField3.setFieldValue("2024-12-25 15:26:39");
        rechargeField3.setFieldType(3);
        rechargeField3.setFieldSort(3);
        rechargeField3.setStatisticalPeriod(2);
        rechargeField3.setRecentDays(3);
        rechargeField3.setIsSelectAmountLimit(0);
        rechargeField3.setAmountLimit(BigDecimal.ZERO);
        rechargeFieldList.add(rechargeField3);

        MemberPortrayalFieldDetailsDTO rechargeField4 = new MemberPortrayalFieldDetailsDTO();
        rechargeField4.setField("RECHARGE_COUNT");
        rechargeField4.setFieldName("累计充值次数");
        rechargeField4.setFieldValue("0");
        rechargeField4.setFieldType(3);
        rechargeField4.setFieldSort(4);
        rechargeField4.setStatisticalPeriod(2);
        rechargeField4.setRecentDays(3);
        rechargeField4.setIsSelectAmountLimit(1);
        rechargeField4.setAmountLimit(new BigDecimal("10.00"));
        rechargeFieldList.add(rechargeField4);

        detailsDTO.setRechargeFieldList(rechargeFieldList);

        return detailsDTO;
    }


    public static List<AdditionalCharge> mockAdditionalChargeList() {
        AdditionalCharge additionalCharge = new AdditionalCharge();
        additionalCharge.setChargeName("消毒碗筷");
        additionalCharge.setChargeValue(BigDecimal.valueOf(25.00));
        return Collections.singletonList(additionalCharge);
    }

    public static List<AdditionalCharge> mockTakeoutAdditionalChargeList() {
        AdditionalCharge boxCharge = new AdditionalCharge();
        boxCharge.setChargeName("餐盒费");
        boxCharge.setChargeValue(new BigDecimal("2"));
        AdditionalCharge shipCharge = new AdditionalCharge();
        shipCharge.setChargeName("配送费");
        shipCharge.setChargeValue(new BigDecimal("2"));
        return Lists.newArrayList(boxCharge, shipCharge);
    }

    public static List<ReduceRecord> mockReduceRecordList() {
        ReduceRecord reduceRecord = new ReduceRecord();
        reduceRecord.setName("会员折扣");
        reduceRecord.setAmount(BigDecimal.valueOf(10));
        return Collections.singletonList(reduceRecord);
    }

    public static List<ReduceRecord> mockTakeoutReduceRecordList() {
        ReduceRecord reduceRecord = new ReduceRecord();
        reduceRecord.setName("XX优惠");
        reduceRecord.setAmount(new BigDecimal("4"));
        return Collections.singletonList(reduceRecord);
    }

    public static BigDecimal mockPayable() {
        return BigDecimal.valueOf(27.00);
    }

    public static BigDecimal mockPayChanged() {
        return BigDecimal.ZERO;
    }

    public static BigDecimal mockActuallyPay() {
        return BigDecimal.valueOf(27.00);
    }

    public static List<PayRecord> mockPayRecords() {
        PayRecord payRecord = new PayRecord();
        payRecord.setPayName("现金");
        payRecord.setAmount(BigDecimal.valueOf(17.00));
        return Collections.singletonList(payRecord);
    }

    public static String mockStoreAddress() {
        return "测试门店地址";
    }

    public static String mockStoreTel() {
        return "18000000000";
    }

    public static List<PrintPreCoTableCbDTO.PrintTableDTO> mockPreTableOrders() {
        return Arrays.asList(mockPrintTableDTO(), mockPrintTableDTO());
    }

    private static PrintPreCoTableCbDTO.PrintTableDTO mockPrintTableDTO() {
        PrintPreCoTableCbDTO.PrintTableDTO tableDTO = new PrintPreCoTableCbDTO.PrintTableDTO();
        tableDTO.setOrderRemark(mockOrderRemark());
        tableDTO.setMarkNo(mockMarkNo());
        tableDTO.setOrderNo(mockOrderNo());
        tableDTO.setPersonNumber(mockPersonNumber());
        tableDTO.setOpenTableTime(mockOpenTableTime());
        tableDTO.setTotal(mockTotal());
        return tableDTO;
    }

    public static List<PrintCoTableCbDTO.PrintTableDTO> mockNowTableOrders() {
        PrintCoTableCbDTO.PrintTableDTO printTableDTO1 = new PrintCoTableCbDTO.PrintTableDTO();
        printTableDTO1.setMarkNo(mockMarkNo());
        printTableDTO1.setOrderNo(mockOrderNo());
        printTableDTO1.setPersonNumber(mockPersonNumber());
        printTableDTO1.setOpenTableTime(mockOpenTableTime());
        printTableDTO1.setTotal(mockTotal());
        printTableDTO1.setCheckOutTime(mockCheckoutTime());
        PrintCoTableCbDTO.PrintTableDTO printTableDTO2 = new PrintCoTableCbDTO.PrintTableDTO();
        printTableDTO2.setOrderRemark(mockOrderRemark());
        printTableDTO2.setMarkNo(mockMarkNo());
        printTableDTO2.setOrderNo(mockOrderNo());
        printTableDTO2.setPersonNumber(mockPersonNumber());
        printTableDTO2.setOpenTableTime(mockOpenTableTime());
        printTableDTO2.setTotal(mockTotal());
        printTableDTO2.setCheckOutTime(mockCheckoutTime());
        return Arrays.asList(printTableDTO1, printTableDTO2);
    }

    public static long mockCheckoutTime() {
        return DateTimeUtils.nowMillis();
    }

    public static String mockTakeoutPlatform() {
        return "XX外卖";
    }


    public static String mockTakeoutPlatformOrder() {
        return "#5";
    }

    public static String mockMemberName() {
        return "L******5";
    }

    public static String mockMemberPhone() {
        return "182****5962";
    }

    public static BigDecimal mockMemberCardBalance() {
        return new BigDecimal("99.99");
    }

    public static BigDecimal mockMemberRechargeAmount() {
        return new BigDecimal("9.99");
    }

    public static BigDecimal mockMemberGiftAmount() {
        return new BigDecimal("9.99");
    }

    public static BigDecimal mockMemberSubsidyAmount() {
        return new BigDecimal("9.99");
    }

    public static String mockMemberCardNum() {
        return "8877";
    }


    public static List<MultiMemberPayRecord> mockMemberPayAmountRecords() {
        MultiMemberPayRecord multiMemberPayRecord = new MultiMemberPayRecord();
        multiMemberPayRecord.setMemberCardBalance(mockMemberCardBalance());
        multiMemberPayRecord.setRechargeAmount(mockMemberRechargeAmount());
        multiMemberPayRecord.setGiftAmount(mockMemberRechargeAmount());
        multiMemberPayRecord.setSubsidyAmount(mockMemberSubsidyAmount());
        multiMemberPayRecord.setMemberCardNum(mockMemberCardNum());
        return Lists.newArrayList(multiMemberPayRecord);
    }

    public static String mockDebtUnitName() {
        return "成都掌控者科技有限公司";
    }

    public static String mockDebtContactName() {
        return "李*";
    }

    public static String mockDebtContactTel() {
        return "188****1234";
    }

    // ==================== 退款单相关模拟数据方法 ====================

    public static Long mockRefundTime() {
        return DateTimeUtils.nowMillis();
    }

    public static BigDecimal mockRefundAmount() {
        return new BigDecimal("12.00");
    }

    public static String mockRefundMethod() {
        return "聚合支付";
    }

    public static String mockRefundReason() {
        return "测试退款";
    }

    public static String mockOperatorStaffName() {
        return "XXX";
    }

    public static Long mockOperationTime() {
        return DateTimeUtils.nowMillis();
    }
}
