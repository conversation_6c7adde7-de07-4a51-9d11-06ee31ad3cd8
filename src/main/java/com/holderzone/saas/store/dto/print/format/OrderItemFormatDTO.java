/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:OrderItemFormatDTO.java
 * Date:2019-12-5
 * Author:terry
 */

package com.holderzone.saas.store.dto.print.format;

import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class OrderItemFormatDTO extends FormatDTO {

    @NotNull(message = "单据名称不能为空")
    @ApiModelProperty(value = "单据名称", required = true)
    private FormatMetadata invoiceName;

    @NotNull(message = "备注不能为空")
    @ApiModelProperty(value = "备注", required = true)
    private FormatMetadata remark;

    @NotNull(message = "牌号不能为空")
    @ApiModelProperty(value = "牌号", required = true)
    private FormatMetadata markNo;

    @NotNull(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private FormatMetadata orderNo;

    @NotNull(message = "人数不能为空")
    @ApiModelProperty(value = "人数", required = true)
    private FormatMetadata personNumber;

    @NotNull(message = "商品名称不能为空")
    @ApiModelProperty(value = "商品名称", required = true)
    private FormatMetadata itemName;

    @NotNull(message = "商品数量不能为空")
    @ApiModelProperty(value = "商品数量", required = true)
    private FormatMetadata itemNumber;

    @NotNull(message = "商品属性不能为空")
    @ApiModelProperty(value = "商品属性", required = true)
    private FormatMetadata itemProperty;

    @NotNull(message = "商品备注不能为空")
    @ApiModelProperty(value = "商品备注", required = true)
    private FormatMetadata itemRemark;

    @NotNull(message = "出餐码不能为空")
    @ApiModelProperty(value = "出餐码", required = true)
    private FormatMetadata foodFinishBarCode;

    @NotNull(message = "操作员不能为空")
    @ApiModelProperty(value = "操作员", required = true)
    private FormatMetadata operator;

    @NotNull(message = "下单时间不能为空")
    @ApiModelProperty(value = "下单时间", required = true)
    private FormatMetadata orderTime;

    @NotNull(message = "打印时间不能为空")
    @ApiModelProperty(value = "打印时间", required = true)
    private FormatMetadata printTime;

    @ApiModelProperty(value = "美团打印小票提示")
    private FormatMetadata orderPrompt;

    @ApiModelProperty(value = "套餐子菜品显示制作数量", required = true)
    private FormatMetadata showMakeNum;

    @ApiModelProperty(value = "送餐时间", required = true)
    private FormatMetadata dinnerTime;

    @ApiModelProperty(value = "单据类型", required = true)
    private FormatMetadata orderTypeName;

    @ApiModelProperty(value = "显示赠送菜品", required = true)
    private FormatMetadata showGiftNum;

    @Override
    public void applyDefault() {
        super.applyDefault();
        if (invoiceName == null) {
            invoiceName = new FormatMetadata(2, 1, false);
        }
        if (remark == null) {
            remark = new FormatMetadata(2, 0, false);
        }
        if (markNo == null) {
            markNo = new FormatMetadata(2, 1, false);
        }
        if (orderNo == null) {
            orderNo = new FormatMetadata(1, 0, false);
        }
        if (personNumber == null) {
            personNumber = new FormatMetadata(2, 2, false);
        }
        if (itemName == null) {
            itemName = new FormatMetadata(2, 0, false);
        }
        if (itemNumber == null) {
            itemNumber = new FormatMetadata(2, 0, false);
        }
        if (itemProperty == null) {
            itemProperty = new FormatMetadata(2, 0, false);
        }
        if (itemRemark == null) {
            itemRemark = new FormatMetadata(2, 0, false);
        }
        if (foodFinishBarCode == null) {
            foodFinishBarCode = new FormatMetadata(1, 1, 1, false, false);
        }
        if (operator == null) {
            operator = new FormatMetadata(1, 0, false);
        }
        if (orderTime == null) {
            orderTime = new FormatMetadata(1, 0, false);
        }
        if (printTime == null) {
            printTime = new FormatMetadata(1, 2, false);
        }
        if (null == orderPrompt) {
            orderPrompt = new FormatMetadata(1, 0, false);
        }
        if (showMakeNum == null) {
            showMakeNum = new FormatMetadata(1, 1, false);
        }
        if (dinnerTime == null) {
            dinnerTime = new FormatMetadata(2, 1, false);
        }
        if (orderTypeName == null) {
            orderTypeName = new FormatMetadata(2, 1, false);
        }
        if (showGiftNum == null) {
            showGiftNum = new FormatMetadata(1, 1, false, false);
        }
    }
}
