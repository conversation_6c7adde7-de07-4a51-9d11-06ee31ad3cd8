package com.holderzone.saas.store.dto.deposit.req;

import com.holderzone.saas.store.dto.deposit.resp.GoodsRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class DepositCreateReqDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @NotEmpty(message = "storeGuid 不得为空")
    @ApiModelProperty(value = "storeGuid")
    private String storeGuid;

    @NotEmpty(message = "会员Guid不得为空")
    @ApiModelProperty(value = "会员Guid")
    private String memberGuid;

    @ApiModelProperty(value = "会员头像地址")
    private String headPortrait;

    @NotEmpty(message = "寄存商品集合不得为空")
    @ApiModelProperty(value = "寄存商品集合")
    private List<GoodsRespDTO> goods;

    @ApiModelProperty(value = "寄存备注")
    private String remark;

    @NotNull(message = "会员姓名不得为空")
    @ApiModelProperty(value = "会员姓名")
    private String customerName;

    @NotNull(message = "电话号码不得为空")
    @ApiModelProperty(value = "电话号码")
    private String phoneNum;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单号")
    private String orderNo;
}
