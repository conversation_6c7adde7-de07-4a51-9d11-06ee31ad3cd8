package com.holderzone.saas.store.dto.organization;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className BrandDTO
 * @date 19-1-2 下午5:25
 * @description 品牌DTO
 * @program holder-saas-store-organization
 */
@ApiModel("品牌DTO")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BrandDTO implements Serializable {

    private static final long serialVersionUID = 4302316796765220094L;

    @NotNull(message = "更新时品牌guid不能为空", groups = Update.class)
    @ApiModelProperty(value = "品牌guid（更新时必传）")
    private String guid;

    @ApiModelProperty(value = "MDM 生成的MDM")
    private String uuid;

    @NotNull(message = "品牌名称不能为空")
    @ApiModelProperty(value = "品牌名称", required = true)
    private String name;

    @ApiModelProperty(value = "品牌介绍")
    private String description;

    @ApiModelProperty(value = "品牌logo（oss下载地址）")
    private String logoUrl;

    @ApiModelProperty(value = "是否启用（默认为1-已启用）")
    private Boolean isEnable;

    @ApiModelProperty(value = "是否删除（默认为0-未删除）")
    private Boolean isDeleted;

    @ApiModelProperty(value = "创建人guid")
    private String createUserGuid;

    @ApiModelProperty(value = "修改人guid")
    private String modifiedUserGuid;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "企业经营类型")
    private String mchntTypeCode;

    @ApiModelProperty(value = "关联门店信息列表")
    private List<StoreDTO> storeList;

    /**
     * 销售模式: 1 普通模式 2 菜谱方案
     */
    @ApiModelProperty(value = "销售模式: 1 普通模式 2 菜谱方案")
    private Integer salesModel;

    /**
     * 门店是强制扎帐 0 否 1 是
     */
    private Integer isBuAccounts;
    /**
     * 门店上缴现金是否显示 0 否 1 是
     */
    private Integer isShowCash;

    /**
     * 多人交接班 0 否 1是
     */
    private Integer isMultiHandover;

    /**
     * 是否授权京东店铺
     */
    private Boolean jdAuth = false;

    /**
     * 是否启用餐段早市 0 否 1 是
     */
    private Integer isMorning;

    /**
     * 餐段早市开始时间
     */
    private String morningStartTime;

    /**
     * 是否启用餐段午市 0 否 1 是
     */
    private Integer isNoon;

    /**
     * 餐段午市开始时间
     */
    private String noonStartTime;

    /**
     * 是否启用餐段下午茶 0 否 1 是
     */
    private Integer isAfternoon;

    /**
     * 餐段下午茶开始时间
     */
    private String afternoonStartTime;

    /**
     * 是否启用餐段晚市 0 否 1 是
     */
    private Integer isEvening;

    /**
     * 餐段晚市开始时间
     */
    private String eveningStartTime;

    /**
     * 是否启用餐段夜宵 0 否 1 是
     */
    private Integer isNight;

    /**
     * 餐段夜宵开始时间
     */
    private String nightStartTime;

    public interface Update {
    }

    public BrandDTO(String guid, String uuid, String name, String description, String logoUrl, Boolean isEnable, Boolean isDeleted, String createUserGuid, String modifiedUserGuid, LocalDateTime gmtCreate, LocalDateTime gmtModified, String mchntTypeCode, List<StoreDTO> storeList, Integer salesModel, Integer isBuAccounts, Integer isShowCash, Integer isMultiHandover, Boolean jdAuth) {
        this.guid = guid;
        this.uuid = uuid;
        this.name = name;
        this.description = description;
        this.logoUrl = logoUrl;
        this.isEnable = isEnable;
        this.isDeleted = isDeleted;
        this.createUserGuid = createUserGuid;
        this.modifiedUserGuid = modifiedUserGuid;
        this.gmtCreate = gmtCreate;
        this.gmtModified = gmtModified;
        this.mchntTypeCode = mchntTypeCode;
        this.storeList = storeList;
        this.salesModel = salesModel;
        this.isBuAccounts = isBuAccounts;
        this.isShowCash = isShowCash;
        this.isMultiHandover = isMultiHandover;
        this.jdAuth = jdAuth;
    }
}
