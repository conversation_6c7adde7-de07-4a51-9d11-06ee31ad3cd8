package com.holderzone.saas.store.dto.print;

import com.holderzone.saas.store.dto.item.resp.TypeItemListDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterDTO
 * @date 2018/7/26 14:29
 * @description
 * @program holder-saas-store-print
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder(toBuilder = true)
@ApiModel(description = "打印机实体")
public class PrinterDTO {

    @ApiModelProperty(value = "门店guid; 添加必传; 修改选传", required = true, example = "一个已有的门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称", required = true, example = "xxx门店")
    private String storeName;

    @ApiModelProperty(value = "设备编号; 添加打印机, deviceId为当前T1的设备编号", required = true)
    private String deviceId;

    @ApiModelProperty(value = "打印机guid; 添加不传; 修改必传")
    private String printerGuid;

    @ApiModelProperty(value = "打印机名称; 添加必传; 修改选传;", example = "前台打印机")
    private String printerName;

    @ApiModelProperty(value = "打印业务类型; 新增必传; 参数: 0前台打印; 1后厨打印; 2标签打印; 3复合业务", required = true, example = "1")
    private Integer businessType;

    @ApiModelProperty(value = "打印机类型; 新增必传; 修改选传; 可选参数: 0/本机; 1/网络; 2/USB 3/云打印", required = true, example = "0")
    private Integer printerType;

    @ApiModelProperty(value = "打印机ip; 如果打印机类型为网络打印机, 新增必传, 修改选传", example = "*************")
    private String printerIp;

    @ApiModelProperty(value = "打印机port; 如果为网络打印机, 新增必传, 修改选传;", example = "9100")
    private Integer printerPort;

    @ApiModelProperty(value = "打印次数； 新增必传； 修改选传", example = "1")
    private Integer printCount;

    @ApiModelProperty(value = "打印纸张类型; 新增必传; 修改选传; 参数: 80; 58; 40*30; 30*20", example = "1")
    private String printPage;

    @ApiModelProperty(value = "打印方式(切纸方式); 新增必传; 修改选传; 参数: 1/整单; 2/一菜一单; 3/一种类型一单; 4/一份数量一单; 默认1/整单", example = "1")
    private Integer printCut;

    @ApiModelProperty(value = "员工guid; 必传", required = true)
    private String staffGuid;

    /**
     * @see InvoiceTypeEnum
     */
    @ApiModelProperty(value = "打印单据; 必传; 7,8结账单 80点菜单")
    private List<Integer> arrayOfInvoiceType;

    /**
     * @see com.holderzone.saas.store.enums.print.PrinterAreaTypeEnum
     */
    @ApiModelProperty(value = "区域类型 0：按桌台 1：按区域")
    private Integer areaType;

    @ApiModelProperty(value = "菜品guid集合; 只用于前端传参过来, 不用做后台业务处理, 后台先转为dishList后用List<Dish>处理")
    private List<String> arrayOfItemGuid;

    @ApiModelProperty(value = "区域guid集合; 只用于前端传参过来, 不用做后台业务处理, 后台先转为areaList后用List<Area>处理")
    private List<String> arrayOfAreaGuid;

    @ApiModelProperty(value = "桌台guid集合; 只用于前端传参过来, 不用做后台业务处理, 后台先转为areaList后用List<Table>处理")
    private List<String> arrayOfTableGuid;

    @ApiModelProperty(value = "票据集合")
    private List<PrinterInvoiceDTO> arrayOfInvoiceDTO;

    @ApiModelProperty(value = "菜品集合")
    private List<PrinterItemDTO> arrayOfItemDTO;

    @ApiModelProperty(value = "区域集合")
    private List<PrinterAreaDTO> arrayOfAreaDTO;

    @ApiModelProperty(value = "桌台集合")
    private List<PrinterTableDTO> arrayOfTableDTO;

    @ApiModelProperty(value = "是否需要打印挂起单：0=否，1=是")
    private Boolean isPrintHangUp;

    @ApiModelProperty(value = "可绑定菜品信息集合")
    private List<TypeItemListDTO> typeItemListDTOS;

    @ApiModelProperty(value = "设备编号")
    private String deviceNo;

    @ApiModelProperty(value = "设备密钥")
    private String deviceKey;

    @ApiModelProperty(value = "设备厂商类型 1飞蛾 2商米")
    private Integer manufacturersType;

    @ApiModelProperty(value = "设备型号")
    private String deviceModel;

    @ApiModelProperty(value = "设备状态 0：离线 1：在线，工作状态正常 2：在线，工作状态不正常")
    private Integer deviceState;

    @ApiModelProperty(value = "打印机实体列表")
    private List<PrinterDTO> printerDTOList;

    @ApiModelProperty(value = "打印价格 0不打印 1打印")
    private Boolean printPriceType;

    @ApiModelProperty(value = "部分退款是否打印退菜单")
    private Integer partRefundPrintFlag;

    @ApiModelProperty(value = "是否选择打印外卖异常单")
    private Boolean isTakeawayException;

    public PrinterDTO(String storeGuid, String storeName, String deviceId, String printerGuid, String printerName, Integer businessType, Integer printerType, String printerIp, Integer printerPort, Integer printCount, String printPage, Integer printCut, String staffGuid, List<Integer> arrayOfInvoiceType, Integer areaType, List<String> arrayOfItemGuid, List<String> arrayOfAreaGuid, List<String> arrayOfTableGuid, List<PrinterInvoiceDTO> arrayOfInvoiceDTO, List<PrinterItemDTO> arrayOfItemDTO, List<PrinterAreaDTO> arrayOfAreaDTO, List<PrinterTableDTO> arrayOfTableDTO, Boolean isPrintHangUp, List<TypeItemListDTO> typeItemListDTOS, String deviceNo, String deviceKey, Integer manufacturersType, String deviceModel, Integer deviceState, List<PrinterDTO> printerDTOList, Boolean printPriceType, Integer partRefundPrintFlag) {
        this.storeGuid = storeGuid;
        this.storeName = storeName;
        this.deviceId = deviceId;
        this.printerGuid = printerGuid;
        this.printerName = printerName;
        this.businessType = businessType;
        this.printerType = printerType;
        this.printerIp = printerIp;
        this.printerPort = printerPort;
        this.printCount = printCount;
        this.printPage = printPage;
        this.printCut = printCut;
        this.staffGuid = staffGuid;
        this.arrayOfInvoiceType = arrayOfInvoiceType;
        this.areaType = areaType;
        this.arrayOfItemGuid = arrayOfItemGuid;
        this.arrayOfAreaGuid = arrayOfAreaGuid;
        this.arrayOfTableGuid = arrayOfTableGuid;
        this.arrayOfInvoiceDTO = arrayOfInvoiceDTO;
        this.arrayOfItemDTO = arrayOfItemDTO;
        this.arrayOfAreaDTO = arrayOfAreaDTO;
        this.arrayOfTableDTO = arrayOfTableDTO;
        this.isPrintHangUp = isPrintHangUp;
        this.typeItemListDTOS = typeItemListDTOS;
        this.deviceNo = deviceNo;
        this.deviceKey = deviceKey;
        this.manufacturersType = manufacturersType;
        this.deviceModel = deviceModel;
        this.deviceState = deviceState;
        this.printerDTOList = printerDTOList;
        this.printPriceType = printPriceType;
        this.partRefundPrintFlag = partRefundPrintFlag;
    }
}
