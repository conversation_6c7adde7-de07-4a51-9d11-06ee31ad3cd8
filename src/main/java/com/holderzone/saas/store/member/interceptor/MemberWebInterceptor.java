package com.holderzone.saas.store.member.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * 会员服务Web拦截器
 * 用于处理企业上下文和ThreadLocal清理
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Slf4j
@Component
public class MemberWebInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            // 获取企业GUID
            String enterpriseGuid = request.getHeader("enterpriseGuid");
            if (enterpriseGuid != null) {
                // 设置企业上下文
                setEnterpriseContext(enterpriseGuid);
                log.debug("设置企业上下文: {}", enterpriseGuid);
            }

            // 获取运营主体GUID
            String operSubjectGuid = request.getHeader("operSubjectGuid");
            if (operSubjectGuid != null) {
                // 设置运营主体上下文
                setOperSubjectContext(operSubjectGuid);
                log.debug("设置运营主体上下文: {}", operSubjectGuid);
            }

        } catch (Exception e) {
            log.error("设置请求上下文失败", e);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 后置处理，可以在这里添加响应处理逻辑
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        try {
            // 清理所有ThreadLocal上下文
            clearAllThreadLocalContext();
            log.debug("ThreadLocal上下文清理完成");
        } catch (Exception e) {
            log.error("清理ThreadLocal上下文失败", e);
        }
    }

    /**
     * 设置企业上下文
     */
    private void setEnterpriseContext(String enterpriseGuid) {
        try {
            // 使用反射调用 UserContextUtils.putErp()
            Class<?> userContextClass = Class.forName("com.holderzone.framework.UserContextUtils");
            Method putErpMethod = userContextClass.getMethod("putErp", String.class);
            putErpMethod.invoke(null, enterpriseGuid);

            // 使用反射调用 EnterpriseIdentifier.setEnterpriseGuid()
            Class<?> enterpriseClass = Class.forName("com.holderzone.framework.EnterpriseIdentifier");
            Method setMethod = enterpriseClass.getMethod("setEnterpriseGuid", String.class);
            setMethod.invoke(null, enterpriseGuid);
        } catch (Exception e) {
            log.warn("设置企业上下文失败", e);
        }
    }

    /**
     * 设置运营主体上下文
     */
    private void setOperSubjectContext(String operSubjectGuid) {
        try {
            // 使用反射调用 ThreadLocalOperSubjectCache.set()
            Class<?> operSubjectClass = Class.forName("com.holderzone.framework.ThreadLocalOperSubjectCache");
            Method setMethod = operSubjectClass.getMethod("set", String.class);
            setMethod.invoke(null, operSubjectGuid);
        } catch (Exception e) {
            log.warn("设置运营主体上下文失败", e);
        }
    }

    /**
     * 清理所有ThreadLocal上下文
     */
    private void clearAllThreadLocalContext() {
        // 清理企业上下文
        clearEnterpriseContext();
        
        // 清理运营主体上下文
        clearOperSubjectContext();
        
        // 清理其他ThreadLocal
        clearOtherThreadLocal();
    }

    /**
     * 清理企业上下文
     */
    private void clearEnterpriseContext() {
        try {
            // 清理 UserContextUtils
            Class<?> userContextClass = Class.forName("com.holderzone.framework.UserContextUtils");
            Method removeMethod = userContextClass.getMethod("remove");
            removeMethod.invoke(null);

            // 清理 EnterpriseIdentifier
            Class<?> enterpriseClass = Class.forName("com.holderzone.framework.EnterpriseIdentifier");
            Method removeMethod2 = enterpriseClass.getMethod("remove");
            removeMethod2.invoke(null);
        } catch (Exception e) {
            log.warn("清理企业上下文失败", e);
        }
    }

    /**
     * 清理运营主体上下文
     */
    private void clearOperSubjectContext() {
        try {
            Class<?> operSubjectClass = Class.forName("com.holderzone.framework.ThreadLocalOperSubjectCache");
            Method removeMethod = operSubjectClass.getMethod("remove");
            removeMethod.invoke(null);
        } catch (Exception e) {
            log.warn("清理运营主体上下文失败", e);
        }
    }

    /**
     * 清理其他ThreadLocal
     */
    private void clearOtherThreadLocal() {
        try {
            // 清理 ThreadLocalCache
            Class<?> cacheClass = Class.forName("com.holderzone.framework.ThreadLocalCache");
            Method removeMethod = cacheClass.getMethod("remove");
            removeMethod.invoke(null);

            // 清理 TraceidUtils
            Class<?> traceClass = Class.forName("com.holderzone.framework.TraceidUtils");
            Method clearMethod = traceClass.getMethod("clear");
            clearMethod.invoke(null);
        } catch (Exception e) {
            log.warn("清理其他ThreadLocal失败", e);
        }
    }
}
